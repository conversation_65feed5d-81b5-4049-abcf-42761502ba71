package com.keyway.adapters.handlers.events.multifamilly.units

import com.keyway.adapters.converters.dto.multifamily.PropertyUnitMessageConverter.toPropertyUnits
import com.keyway.adapters.dtos.units.PropertyUnitMessage
import com.keyway.core.usecases.multifamily.units.SaveOrUpdatePropertyUnits
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.sqs.IMessageHandler
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

class SavePropertyUnitsData(
    private val saveOrUpdatePropertyUnits: SaveOrUpdatePropertyUnits,
) : IMessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val IMPORT_PROCESS = "units_data"
    }

    override fun processMessage(message: Message): Boolean =
        runCatching {
            message
                .body()
                .let {
                    JsonMapper.decode(it, PropertyUnitMessage::class.java)
                }.toPropertyUnits()
                .let(saveOrUpdatePropertyUnits::execute)
            true
        }.getOrElse { error ->
            logger.error("""[SAVE_UNITS_CONSUMER] Error on save units data - import_process:$IMPORT_PROCESS""", error)
            false
        }
}
