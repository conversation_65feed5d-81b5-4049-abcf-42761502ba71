package com.keyway.adapters.handlers.events.tasks

import com.keyway.adapters.dtos.conciliation.RentTaskDataMessage
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.core.usecases.backgorund.tasks.DeleteInactiveListingDataUseCase
import com.keyway.core.usecases.backgorund.tasks.NotifyLastRentByPropertyUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import com.keyway.kommons.sqs.IMessageHandler
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

class RentTasksHandler(
    val useCaseExecutor: UseCaseExecutor,
    private val deleteInactiveListingDataUseCase: DeleteInactiveListingDataUseCase,
    private val notifyLastRentByPropertyUseCase: NotifyLastRentByPropertyUseCase,
) : IMessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun processMessage(message: Message): Boolean =
        runCatching {
            SqsMapper.decode(message.body(), RentTaskDataMessage::class.java).let {
                when (it.type) {
                    "DELETE_DATA" ->
                        useCaseExecutor(
                            useCase = deleteInactiveListingDataUseCase,
                            inputDto = it,
                            inputConverter = { input -> input.mapTo() },
                        )
                    "LAST_RENT_NOTIFICATION" ->
                        useCaseExecutor(
                            useCase = notifyLastRentByPropertyUseCase,
                            inputDto = it,
                            inputConverter = { input -> input.mapTo() },
                        )
                }
            }
        }.onFailure {
            logger.warn("""[RENT_TASK] Error in Rent Task Background Process""", it)
            return false
        }.isSuccess
}
