package com.keyway.adapters.handlers.rest.historical

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.HistoricalConcession
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.usecases.historical.GetHistoricalConcessionByIdsUseCase
import com.keyway.core.usecases.historical.GetHistoricalRents.Input
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class GetHistoricalConcessionByIds(
    private val useCaseExecutor: UseCaseExecutor,
    private val getHistoricalConcessionByIdsUseCase: GetHistoricalConcessionByIdsUseCase,
) {
    companion object {
        const val MAX_DAILY_AMOUNT = 45L
        const val MAX_WEEKLY_AMOUNT = 30L
        const val MAX_MONTHLY_AMOUNT = 30L
    }

    suspend operator fun invoke(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity?,
    ): HistoricalConcession =
        useCaseExecutor(
            useCase = getHistoricalConcessionByIdsUseCase,
            inputDto =
                GetHistoricalConcessionByIdsUseCase.Input(
                    ids = ids,
                    idType = idType,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = periodicity ?: calculatePeriodicity(dateFrom, dateTo),
                ),
            inputConverter = { fixToMaxRange(it) },
            outputConverter = { it },
        )

    private fun calculatePeriodicity(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): HistoricalPeriodicity =
        (dateFrom.until(dateTo, ChronoUnit.DAYS)).let {
            when {
                it > MAX_WEEKLY_AMOUNT * 7 -> HistoricalPeriodicity.MONTHLY
                it > MAX_DAILY_AMOUNT -> HistoricalPeriodicity.WEEKLY
                else -> HistoricalPeriodicity.DAILY
            }
        }

    private fun fixToMaxRange(input: GetHistoricalConcessionByIdsUseCase.Input): GetHistoricalConcessionByIdsUseCase.Input =
        when (input.periodicity) {
            HistoricalPeriodicity.DAILY ->
                input.dateTo.minusDays(MAX_DAILY_AMOUNT)
            HistoricalPeriodicity.WEEKLY ->
                input.dateTo.minusWeeks(MAX_WEEKLY_AMOUNT)
            HistoricalPeriodicity.MONTHLY ->
                input.dateTo.minusMonths(MAX_MONTHLY_AMOUNT)
        }.takeIf { input.dateFrom <= it }
            ?.let { input.copy(dateFrom = it) }
            ?: input
}
