package com.keyway.adapters.handlers.rest.listings

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.usecases.listings.GetFutureAvailabilityUseCase

class GetFutureAvailabilityHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getFutureAvailabilityUseCase: GetFutureAvailabilityUseCase,
) {
    operator fun invoke(
        id: String,
        idType: IdType,
    ): List<BedroomFutureAvailability> =
        useCaseExecutor.invoke(
            useCase = getFutureAvailabilityUseCase,
            inputDto = GetFutureAvailabilityUseCase.Input(id, idType),
            outputConverter = { it },
            inputConverter = { it },
        )
}
