package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.adapters.repositories.utils.ViewsUtils.buildQueryCondition
import com.keyway.adapters.repositories.utils.ViewsUtils.generateViewsNames
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.DateUtils.toEndOfMonth
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class PostgresHistoricalRentRepository(
    private val sqlClient: SqlClient,
) : HistoricalRentRepository {
    private fun LocalDate.toSqlString(): String = "'$this'::DATE"

    private fun getDataSeries(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): String =
        when (periodicity) {
            HistoricalPeriodicity.DAILY -> """
            WITH date_series AS (
                SELECT
                    date_val::DATE AS date_from,
                    date_val::DATE AS date_to
                FROM generate_series(
                    ${dateFrom.toSqlString()},
                    ${dateTo.toSqlString()},
                    INTERVAL '1 DAY'
                ) AS date_val
            )
        """

            HistoricalPeriodicity.WEEKLY -> """
            WITH date_series AS (
                    SELECT
                        date_val::DATE AS date_from,
                        (date_val::DATE + INTERVAL '6 days')::date AS date_to
                    FROM generate_series(
                        DATE_TRUNC('WEEK', ${dateFrom.toSqlString()}),
                        ${dateTo.toSqlString()},
                        INTERVAL '1 WEEK'
                    ) AS date_val
                )
        """

            HistoricalPeriodicity.MONTHLY -> """
            WITH date_series AS (
                SELECT
                    date_val::DATE AS date_from,
                    (date_val::DATE + INTERVAL '1 month' - INTERVAL '1 day')::date AS date_to
                FROM generate_series(
                    DATE_TRUNC('MONTH',${dateFrom.toSqlString()}),
                    ${dateTo.toSqlString()},
                    INTERVAL '1 MONTH'
                ) AS date_val
            )
        """
        }

    override suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            when (idType) {
                IdType.ZIP_CODE, IdType.MSA ->
                    getGeoData(
                        ids,
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                    )
                else ->
                    getPropertyData(
                        ids.joinToString(",") { "'$it'" },
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                        bathrooms,
                        unitCondition,
                    )
            }
        }

    override suspend fun getHistoricalRentsByProperty(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            getGeoData(
                ids,
                idType,
                dateFrom,
                dateTo,
                periodicity,
                rentType,
                null,
                IdType.PROPERTY,
            )
        }

    private fun getGeoData(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        groupByIdType: IdType = idType,
    ): List<HistoricalRentOutput> {
        val condition = buildQueryCondition(idType, ids, dateFrom, dateTo, bedrooms)
        val query =
            """
            ${getDataSeries(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${generateViewsNames(rentType, dateFrom, dateTo)
                .joinToString(" UNION ALL ") { "SELECT * FROM $it $condition"}}
                )
                 SELECT 
                ${groupByIdType.getSqlColumn()} as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                SUM(total_records) as total_records,
                SUM(sft_sum) as total_square_footage,
                SUM(rent_sum) as total_rent,                
                SUM(CASE WHEN sft_sum IS NOT NULL THEN rent_sum ELSE 0 END) as total_rent_with_square_footage,
                SUM(CASE WHEN sft_sum IS NOT NULL THEN 1 ELSE 0 END) as total_records_with_square_footage
            FROM filtered_data join date_series 
                ON filtered_data.date_of_record BETWEEN date_series.date_from AND date_series.date_to 
            GROUP BY ${groupByIdType.getSqlColumn()}, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient
            .getAll(
                query = query,
                params = emptyList(),
                clazz = HistoricalRentOutput::class.java,
            ).let { output ->
                output
                    .takeIf { it.isNotEmpty() }
                    ?.maxBy { it.dateTo }
                    ?.let { lastRecord ->
                        val tempUnit =
                            when (periodicity) {
                                HistoricalPeriodicity.DAILY -> ChronoUnit.DAYS
                                HistoricalPeriodicity.WEEKLY -> ChronoUnit.WEEKS
                                HistoricalPeriodicity.MONTHLY -> ChronoUnit.MONTHS
                            }
                        lastRecord
                            .takeUnless { dateTo.isBetween(lastRecord.dateFrom, lastRecord.dateTo) }
                            ?.copy(
                                dateFrom =
                                    lastRecord.dateFrom.plus(1, tempUnit),
                                dateTo =
                                    lastRecord.dateTo.plus(1, tempUnit).let { newDateTo ->
                                        newDateTo.takeUnless { periodicity == HistoricalPeriodicity.MONTHLY }
                                            ?: newDateTo.toEndOfMonth()
                                    },
                            )
                    }?.let {
                        output + it
                    } ?: output
            }
    }

    private fun getPropertyData(
        sqlIds: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> {
        val query =
            """
                ${getDataSeries(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${buildQuery(
                when (rentType) {
                    RentType.ASKING -> "rent_listing"
                    RentType.EFFECTIVE -> "effective_rent"
                },
                idType,
                sqlIds,
                dateTo,
                dateFrom,
                bedrooms,
                bathrooms,
                unitCondition = unitCondition,
            )}   
                )
                 SELECT 
                id as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                count(*) as total_records,
                SUM(unit_square_footage) as total_square_footage,
                SUM(CASE WHEN unit_square_footage IS NOT NULL THEN rent ELSE 0 END) as total_rent_with_square_footage,
                SUM(CASE WHEN unit_square_footage IS NOT NULL THEN 1 ELSE 0 END) as total_records_with_square_footage,
                SUM(rent) as total_rent,
                ROUND((percentile_cont(0.5) WITHIN GROUP (ORDER BY rent))::numeric, 2) AS rent_median
            FROM filtered_data join date_series 
                ON filtered_data.date_from <= date_series.date_to 
                    AND filtered_data.date_to >= date_series.date_from
            GROUP BY id, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
            clazz = HistoricalRentOutput::class.java,
        )
    }

    private fun buildQuery(
        tableName: String,
        idType: IdType,
        sqlIds: String,
        dateTo: LocalDate,
        dateFrom: LocalDate,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ) = """
        SELECT 
        $tableName.${idType.getSqlColumn()} as id,
        $tableName.id as listing_id,
        $tableName.property_id, 
        $tableName.unit_square_footage, 
        $tableName.rent,
        $tableName.type,
        $tableName.date_from,
        $tableName.date_to
           FROM $tableName
           ${unitCondition?.getUnitConditionJoin(rentAlias = tableName) ?: ""}
           WHERE  $tableName.${idType.getSqlColumn()} IN ($sqlIds)
             AND date_from <= ${dateTo.toSqlString()}
             AND date_to >= ${dateFrom.toSqlString()}
             ${RentUtils.isActiveSql(tableName = tableName)}
             ${bedrooms?.let { " AND $tableName.bedrooms = $it " } ?: ""}
             ${bathrooms?.let { " AND $tableName.bathrooms = $it" } ?: ""}
             ${unitCondition?.getWhere() ?: ""}
        """.trimIndent()
}
