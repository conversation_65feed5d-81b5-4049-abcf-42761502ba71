package com.keyway.application.koin.modules

import com.keyway.adapters.handlers.events.concessions.SaveConcessionsV2Handler
import com.keyway.adapters.handlers.events.listings.SaveGroupedListingHandler
import com.keyway.adapters.handlers.events.multifamilly.SavePropertyData
import com.keyway.adapters.handlers.events.multifamilly.units.SavePropertyUnitsData
import com.keyway.adapters.handlers.events.tasks.RentTasksHandler
import com.keyway.adapters.services.SqsMessageBatchPublisher
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.findQueueConfiguration
import com.keyway.application.koin.ModuleConstants
import com.keyway.core.service.MessageBatchPublisher
import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.aws.config.SqsConfig
import com.keyway.kommons.sqs.IMessageHandler
import com.keyway.kommons.sqs.SqsConsumer
import com.keyway.kommons.sqs.buildSqsClient
import com.keyway.kommons.sqs.configuration.DefaultSqsUrlBuilder
import com.keyway.kommons.sqs.configuration.SqsQueueConfig
import com.keyway.kommons.sqs.configuration.SqsUrlBuilder
import com.keyway.kommons.sqs.processors.OversizeProcessor
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.S3Configuration

object SqsModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single {
                buildSqsClient(get())
            }

            single<SqsUrlBuilder> { DefaultSqsUrlBuilder(get()) }

            single<IMessageHandler>(named(ModuleConstants.PROPERTY_CONCESSIONS_V2_HANDLER)) {
                SaveConcessionsV2Handler(get(), get())
            }

            single<IMessageHandler>(named(ModuleConstants.GROUPED_UNIT_RENT_DATA_HANDLER)) {
                SaveGroupedListingHandler(get(), get(), get())
            }

            single<IMessageHandler>(named(ModuleConstants.RENT_TASKS_DATA_HANDLER)) {
                RentTasksHandler(get(), get(), get())
            }

            single<IMessageHandler>(named(ModuleConstants.PROPERTY_UNITS_DATA_HANDLER)) {
                SavePropertyUnitsData(get())
            }

            single<IMessageHandler>(named(ModuleConstants.PROPERTY_DATA_HANDLER)) {
                SavePropertyData(get())
            }

            single<MessageBatchPublisher> {
                SqsMessageBatchPublisher(get(), get<SqsUrlBuilder>().buildUrl(get<Configuration>().publishQueue))
            }

            single<S3Client> {
                get<AwsConfig>().let { awsConfig ->
                    S3Client
                        .builder()
                        .region(Region.of(awsConfig.region))
                        .serviceConfiguration(
                            S3Configuration
                                .builder()
                                .pathStyleAccessEnabled(true)
                                .build(),
                        ).credentialsProvider {
                            AwsBasicCredentials.create(awsConfig.accessKey, awsConfig.secretKey)
                        }.build()
                }
            }

            single(named(ModuleConstants.CONSUMERS)) {
                setOfNotNull(
                    get<SqsConfig>().getSqsConfig("property_concessions_v2")?.let { sqsConfig ->
                        SqsConsumer(
                            sqs = get(),
                            sqsQueueConfig = sqsConfig,
                            sqsUrlBuilder = get(),
                            messageHandler = get(named(ModuleConstants.PROPERTY_CONCESSIONS_V2_HANDLER)),
                            messageProcessor =
                                OversizeProcessor(
                                    s3Client = get(),
                                    sqsQueueConfig = sqsConfig,
                                ),
                        )
                    },
                    get<SqsConfig>()
                        .getSqsConfig("grouped_unit_rent_data")
                        ?.let { sqsConfig ->
                            SqsConsumer(
                                sqs = get(),
                                sqsQueueConfig = sqsConfig,
                                sqsUrlBuilder = get(),
                                messageHandler = get(named(ModuleConstants.GROUPED_UNIT_RENT_DATA_HANDLER)),
                                messageProcessor =
                                    OversizeProcessor(
                                        s3Client = get(),
                                        sqsQueueConfig = sqsConfig,
                                    ),
                            )
                        },
                    get<SqsConfig>().getSqsConfig("background_rent_tasks")?.let { sqsConfig ->
                        SqsConsumer(
                            sqs = get(),
                            sqsQueueConfig = sqsConfig,
                            sqsUrlBuilder = get(),
                            messageHandler = get(named(ModuleConstants.RENT_TASKS_DATA_HANDLER)),
                        )
                    },
                    get<SqsConfig>().getSqsConfig("property_units_data")?.let { sqsConfig ->
                        SqsConsumer(
                            sqs = get(),
                            sqsQueueConfig = sqsConfig,
                            sqsUrlBuilder = get(),
                            messageHandler = get(named(ModuleConstants.PROPERTY_UNITS_DATA_HANDLER)),
                        )
                    },
                    get<SqsConfig>().getSqsConfig("property_data")?.let { sqsConfig ->
                        SqsConsumer(
                            sqs = get(),
                            sqsQueueConfig = sqsConfig,
                            sqsUrlBuilder = get(),
                            messageHandler = get(named(ModuleConstants.PROPERTY_DATA_HANDLER)),
                        )
                    },
                )
            }
        }

    private fun SqsConfig.getSqsConfig(sqsName: String) =
        this.findQueueConfiguration(sqsName).takeIf {
            it.shouldAddConsumer()
        }

    private fun SqsQueueConfig.shouldAddConsumer() = this.workers > 0
}
