package com.keyway.core.dto

import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalConcessionSummary(
    val propertyId: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionPercentage: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class MsaHistoricalConcessionSummary(
    val msaCode: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionPercentage: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class ZipHistoricalConcessionSummary(
    val zipCode: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionPercentage: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class HistoricalConcessionValue(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val totalProperties: Int,
    val totalPropertiesWithConcession: Int,
    val avgConcessionValue: BigDecimal,
    val avgConcessionPercentage: BigDecimal,
)

sealed interface HistoricalConcession {
    val avgConcessionValue: BigDecimal
    val avgConcessionPercentage: BigDecimal
    val values: List<HistoricalConcessionValue>
}
