package com.keyway.core.ports.repositories

import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import java.time.LocalDate

interface ListingsRepository {
    fun save(rentListing: RentListing)

    fun save(rentListings: List<RentListing>)

    fun update(rentListings: List<RentListing>)

    fun delete(ids: List<String>)

    fun findMostRecentListingByPropertyAndUnit(
        propertyId: String,
        type: RentListingType = RentListingType.UNIT,
        unitId: String,
        from: LocalDate,
    ): RentListing?

    fun findFutureListingByPropertyAndUnit(
        propertyId: String,
        type: RentListingType = RentListingType.UNIT,
        unitId: String,
        from: LocalDate,
    ): RentListing?

    suspend fun findLastListingByProperties(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing>

    suspend fun findListingsForProperties(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing>

    fun findListingsForPropertiesAndUnits(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing>

    fun findInactiveListings(limit: Int): List<RentListing>

    fun futureAvailabilityByBedroom(
        id: String,
        idType: IdType,
    ): List<BedroomFutureAvailability>
}
