package com.keyway.core.usecases.historical

import com.keyway.core.dto.HistoricalConcession
import com.keyway.core.dto.HistoricalConcessionSummary
import com.keyway.core.dto.HistoricalConcessionValue
import com.keyway.core.dto.MsaHistoricalConcessionSummary
import com.keyway.core.dto.ZipHistoricalConcessionSummary
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.time.LocalDate

class GetHistoricalConcessionByIdsUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<
        GetHistoricalConcessionByIdsUseCase.Input,
        HistoricalConcession,
    > {
    override suspend fun execute(input: Input): HistoricalConcession =
        withContext(Dispatchers.IO) {
            val askingDeferred =
                async {
                    historicalRentRepository.getHistoricalRentsByProperty(
                        ids = input.ids,
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.ASKING,
                    )
                }

            val effectiveDeferred =
                async {
                    historicalRentRepository.getHistoricalRentsByProperty(
                        ids = input.ids,
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.EFFECTIVE,
                    )
                }

            val (asking, effective) = awaitAll(askingDeferred, effectiveDeferred)

            val effectiveByProperty = effective.byProperty()

            val values =
                asking.groupBy { it.dateFrom to it.dateTo }.map { (key, value) ->
                    val rentValues =
                        value.map {
                            RentValue(
                                askingRent = Money.of(it.totalRent),
                                effectiveRent = Money.of(effectiveByProperty[it.getKey()]?.totalRent ?: it.totalRent),
                            )
                        }

                    HistoricalConcessionValue(
                        dateFrom = key.first,
                        dateTo = key.second,
                        totalProperties = rentValues.size,
                        totalPropertiesWithConcession = rentValues.count { it.hasConcession },
                        avgConcessionValue = rentValues.sumOf { it.difference.value } / rentValues.size.toBigDecimal(),
                        avgConcessionPercentage = rentValues.sumOf { it.percentage.value } / rentValues.size.toBigDecimal(),
                    )
                }

            val avgConcessionValue = values.sumOf { it.avgConcessionValue } / values.size.toBigDecimal()
            val avgConcessionPercentage = values.sumOf { it.avgConcessionPercentage } / values.size.toBigDecimal()

            when (input.idType) {
                IdType.PROPERTY ->
                    HistoricalConcessionSummary(
                        propertyId = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionPercentage = avgConcessionPercentage,
                        values = values,
                    )
                IdType.ZIP_CODE ->
                    ZipHistoricalConcessionSummary(
                        zipCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionPercentage = avgConcessionPercentage,
                        values = values,
                    )
                IdType.MSA ->
                    MsaHistoricalConcessionSummary(
                        msaCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionPercentage = avgConcessionPercentage,
                        values = values,
                    )
            }
        }

    data class RentValue(
        val askingRent: Money,
        val effectiveRent: Money,
    ) {
        val difference = askingRent - effectiveRent
        val percentage = difference / askingRent
        val hasConcession = difference > Money.of(BigDecimal.ZERO)
    }

    private fun HistoricalRentOutput.getKey() = "${this.id}-${this.dateFrom}-${this.dateTo}"

    private fun List<HistoricalRentOutput>.byProperty() = this.associateBy { it.getKey() }

    data class Input(
        val ids: Set<String>,
        val idType: IdType,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
        val periodicity: HistoricalPeriodicity,
    )
}
