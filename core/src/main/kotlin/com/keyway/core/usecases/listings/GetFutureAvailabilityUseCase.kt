package com.keyway.core.usecases.listings

import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCase

class GetFutureAvailabilityUseCase(
    private val listingsRepository: ListingsRepository,
) : UseCase<
        GetFutureAvailabilityUseCase.Input,
        List<BedroomFutureAvailability>,
    > {
    override fun execute(input: Input): List<BedroomFutureAvailability> = listingsRepository.futureAvailabilityByBedroom(input.id, input.idType)

    data class Input(
        val id: String,
        val idType: IdType,
    )
}
