package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.ListingsRepository
import kong.unirest.GenericType
import kong.unirest.Unirest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.MockedEntityFactory
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class GeoRentMetricsRouterTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()

    @BeforeEach
    fun setUp() {
        // Create test data for future availability
        val futureDate1 = LocalDate.now().plusDays(30)
        val futureDate2 = LocalDate.now().plusDays(60)
        val futureDate3 = LocalDate.now().plusDays(90)

        // Create listings for ZIP code testing
        val zipCodeListing1 =
            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                zipCode = "12345",
                msaCode = "MSA001",
                typeId = "unit-1",
                availableIn = futureDate1,
                isActive = true,
                dateFrom = LocalDate.now(),
                dateTo = LocalDate.now(),
            )

        val zipCodeListing2 =
            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027627",
                type = RentListingType.UNIT,
                zipCode = "12345",
                msaCode = "MSA001",
                typeId = "unit-2",
                availableIn = futureDate2,
                isActive = true,
                dateFrom = LocalDate.now(),
                dateTo = LocalDate.now(),
            )

        val zipCodeListing3 =
            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027628",
                type = RentListingType.UNIT,
                zipCode = "12345",
                msaCode = "MSA001",
                typeId = "unit-3",
                availableIn = futureDate3,
                isActive = true,
                dateFrom = LocalDate.now(),
                dateTo = LocalDate.now(),
            )

        // Create listings for MSA code testing
        val msaCodeListing1 =
            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027629",
                type = RentListingType.UNIT,
                zipCode = "67890",
                msaCode = "MSA002",
                typeId = "unit-4",
                availableIn = futureDate1,
                isActive = true,
                dateFrom = LocalDate.now(),
                dateTo = LocalDate.now(),
            )

        val msaCodeListing2 =
            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027630",
                type = RentListingType.UNIT,
                zipCode = "67890",
                msaCode = "MSA002",
                typeId = "unit-5",
                availableIn = futureDate2,
                isActive = true,
                dateFrom = LocalDate.now(),
                dateTo = LocalDate.now(),
            )

        listingsRepository.save(
            listOf(
                zipCodeListing1,
                zipCodeListing2,
                zipCodeListing3,
                msaCodeListing1,
                msaCodeListing2,
            ),
        )
    }

    @Test
    fun `should return future availability for ZIP code`() {
        // Given
        val zipCode = "12345"
        val givenUrl = "${localUrl()}/zip-codes/$zipCode/bedroom-future-availability"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(object : GenericType<List<BedroomFutureAvailability>>() {})

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(200, result.status)
        assertNotNull(result.body)

        // Should have 3 units available across different dates
        val totalUnits = result.body.flatMap { it.values.values }.sum()
        assertTrue(totalUnits >= 3, "Expected at least 3 units available, but got $totalUnits")
    }

    @Test
    fun `should return future availability for MSA code`() {
        // Given
        val msaCode = "MSA002"
        val givenUrl = "${localUrl()}/msa-codes/$msaCode/bedroom-future-availability"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(object : GenericType<List<BedroomFutureAvailability>>() {})

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(200, result.status)
        assertNotNull(result.body)

        // Should have 2 units available across different dates
        val totalUnits = result.body.flatMap { it.values.values }.sum()
        assertTrue(totalUnits >= 2, "Expected at least 2 units available, but got $totalUnits")
    }

    @Test
    fun `should return empty map for non-existent ZIP code`() {
        // Given
        val nonExistentZipCode = "99999"
        val givenUrl = "${localUrl()}/zip-codes/$nonExistentZipCode/bedroom-future-availability"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(object : GenericType<List<BedroomFutureAvailability>>() {})

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(200, result.status)
        assertNotNull(result.body)
        assertTrue(result.body.isEmpty(), "Expected empty map for non-existent ZIP code")
    }

    @Test
    fun `should return empty map for non-existent MSA code`() {
        // Given
        val nonExistentMsaCode = "MSA999"
        val givenUrl = "${localUrl()}/msa-codes/$nonExistentMsaCode/bedroom-future-availability"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(object : GenericType<List<BedroomFutureAvailability>>() {})

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(200, result.status)
        assertNotNull(result.body)
        assertTrue(result.body.isEmpty(), "Expected empty map for non-existent MSA code")
    }
}
